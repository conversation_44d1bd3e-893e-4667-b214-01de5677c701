#!/usr/bin/env python3
"""
验证rerank API修复
"""
import asyncio
import aiohttp

async def test_single_rerank():
    """测试单个rerank调用"""
    url = "http://*************:6000/v1/rerank/score"
    headers = {
        "Authorization": "Bearer sk-7a2f8e1d4c9b3a6x9y5z2w8q4r6t7u8i9o0p",
        "Content-Type": "application/json",
        "accept": "application/json"
    }
    
    payload = {
        "model": "qwen_rerank",
        "text_1": "什么是人工智能？",
        "text_2": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"
    }
    
    print("测试单个rerank API调用")
    print(f"URL: {url}")
    print(f"Query: {payload['text_1']}")
    print(f"Document: {payload['text_2']}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                print(f"\nHTTP状态码: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ API调用成功!")
                    print(f"完整响应: {result}")
                    
                    # 提取分数
                    data = result.get("data", [])
                    if data and len(data) > 0:
                        score = data[0].get("score", 0.0)
                        print(f"提取的分数: {score:.4f}")
                    else:
                        print("❌ 无法提取分数")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ API调用失败: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def test_multiple_documents():
    """测试多个文档的rerank"""
    url = "http://*************:6000/v1/rerank/score"
    headers = {
        "Authorization": "Bearer sk-7a2f8e1d4c9b3a6x9y5z2w8q4r6t7u8i9o0p",
        "Content-Type": "application/json",
        "accept": "application/json"
    }
    
    query = "什么是人工智能？"
    documents = [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "今天天气很好，适合出去散步。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。"
    ]
    
    print("\n" + "="*50)
    print("测试多个文档的rerank")
    print(f"查询: {query}")
    print(f"文档数量: {len(documents)}")
    
    results = []
    
    try:
        async with aiohttp.ClientSession() as session:
            for i, doc in enumerate(documents):
                payload = {
                    "model": "qwen_rerank",
                    "text_1": query,
                    "text_2": doc
                }
                
                print(f"\n测试文档 {i+1}: {doc[:30]}...")
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        data = result.get("data", [])
                        if data and len(data) > 0:
                            score = data[0].get("score", 0.0)
                            results.append({"index": i, "score": score, "document": doc})
                            print(f"  分数: {score:.4f}")
                        else:
                            print(f"  ❌ 无法提取分数")
                    else:
                        error_text = await response.text()
                        print(f"  ❌ 失败: {error_text}")
        
        # 排序结果
        results.sort(key=lambda x: x["score"], reverse=True)
        
        print(f"\n最终排序结果:")
        for i, result in enumerate(results):
            print(f"  排名 {i+1}: 分数 {result['score']:.4f}")
            print(f"    文档: {result['document'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 开始验证Rerank API修复")
    
    # 测试单个调用
    success1 = await test_single_rerank()
    
    # 测试多个文档
    success2 = await test_multiple_documents()
    
    if success1 and success2:
        print("\n✅ 所有测试通过！Rerank API修复成功！")
    else:
        print("\n❌ 部分测试失败")

if __name__ == "__main__":
    asyncio.run(main())
