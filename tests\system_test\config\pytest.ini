[tool:pytest]
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
filterwarnings =
    ignore::DeprecationWarning
    ignore::PytestDeprecationWarning
    ignore::PytestRemovedIn9Warning
markers =
    asyncio: marks tests as async
    slow: marks tests as slow
    integration: marks tests as integration tests
