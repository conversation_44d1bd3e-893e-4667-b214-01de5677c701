#!/usr/bin/env python3
"""
测试rerank功能
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.rag.rerank import openai_rerank
from common.config import settings, get_rerank_config


async def test_rerank_api():
    """测试rerank API调用"""
    print("=" * 50)
    print("测试Rerank API")
    print("=" * 50)
    
    # 测试文档
    query = "什么是人工智能？"
    documents = [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。",
        "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
        "自然语言处理是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
        "今天天气很好，适合出去散步。"
    ]
    
    print(f"查询: {query}")
    print(f"文档数量: {len(documents)}")
    print("\n原始文档:")
    for i, doc in enumerate(documents):
        print(f"{i}: {doc}")
    
    try:
        # 获取rerank配置
        rerank_config = get_rerank_config()
        print(f"\nRerank配置:")
        print(f"  API Base: {rerank_config.get('api_base')}")
        print(f"  Model: {rerank_config.get('model')}")
        print(f"  Top K: {rerank_config.get('top_k')}")
        
        # 调用rerank API
        print("\n调用Rerank API...")
        results = await openai_rerank(
            query=query,
            documents=documents,
            top_n=3,
            api_key=rerank_config.get("api_key"),
            model=rerank_config.get("model"),
            base_url=rerank_config.get("api_base")
        )
        
        print(f"\nRerank结果:")
        for result in results:
            index = result["index"]
            score = result["relevance_score"]
            doc = documents[index]
            print(f"  排名 {results.index(result) + 1}: 索引 {index}, 分数 {score:.4f}")
            print(f"    文档: {doc}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Rerank测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_rerank_config():
    """测试rerank配置"""
    print("=" * 50)
    print("测试Rerank配置")
    print("=" * 50)
    
    print(f"Rerank启用状态: {settings.rerank_enabled}")
    print(f"Rerank提供商: {settings.rerank_provider}")
    print(f"Rerank模型: {settings.openai_rerank_model}")
    print(f"Rerank API Base: {settings.openai_rerank_api_base}")
    print(f"Rerank API Key: {'***' if settings.openai_rerank_api_key != 'your_api_key_here' else '未配置'}")
    
    rerank_config = get_rerank_config()
    print(f"\n完整Rerank配置:")
    for key, value in rerank_config.items():
        if 'key' in key.lower():
            value = '***' if value != 'your_api_key_here' else '未配置'
        print(f"  {key}: {value}")


async def main():
    """主函数"""
    print("🚀 开始测试Rerank功能")
    
    # 测试配置
    await test_rerank_config()
    
    # 测试API调用
    success = await test_rerank_api()
    
    if success:
        print("\n✅ Rerank功能测试成功！")
    else:
        print("\n❌ Rerank功能测试失败！")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
