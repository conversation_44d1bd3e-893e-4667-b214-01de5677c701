#!/usr/bin/env python3
"""
测试配置读取
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from common.config import settings, find_env_file, get_project_root


def test_config():
    """测试配置读取"""
    print("=" * 50)
    print("测试配置读取")
    print("=" * 50)

    # 检查路径
    print("路径信息:")
    print(f"  项目根目录: {get_project_root()}")
    print(f"  找到的.env文件: {find_env_file()}")
    print(f"  当前工作目录: {Path.cwd()}")

    # 手动加载.env文件测试
    env_file = find_env_file()
    if env_file:
        print(f"\n手动加载.env文件: {env_file}")
        from dotenv import load_dotenv
        load_dotenv(env_file, override=True)
        print("  加载完成，检查环境变量:")
        for var in ["OPENAI_RERANK_API_BASE", "OPENAI_RERANK_API_KEY", "OPENAI_RERANK_MODEL"]:
            value = os.getenv(var, "未设置")
            print(f"    {var}: {value}")
    
    # 检查环境变量
    print("环境变量:")
    rerank_vars = [
        "OPENAI_RERANK_API_BASE",
        "OPENAI_RERANK_API_KEY", 
        "OPENAI_RERANK_MODEL",
        "RERANK_ENABLED",
        "RERANK_PROVIDER",
        "RERANK_MODEL"
    ]
    
    for var in rerank_vars:
        value = os.getenv(var, "未设置")
        print(f"  {var}: {value}")
    
    print("\n配置对象属性:")
    print(f"  openai_rerank_api_base: {settings.openai_rerank_api_base}")
    print(f"  openai_rerank_api_key: {settings.openai_rerank_api_key}")
    print(f"  openai_rerank_model: {settings.openai_rerank_model}")
    print(f"  rerank_enabled: {settings.rerank_enabled}")
    print(f"  rerank_provider: {settings.rerank_provider}")
    print(f"  rerank_model: {settings.rerank_model}")
    
    # 检查.env文件内容
    env_file = project_root / ".env"
    if env_file.exists():
        print(f"\n.env文件内容 ({env_file}):")
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                if 'RERANK' in line.upper():
                    print(f"  {i:3d}: {line.rstrip()}")


if __name__ == "__main__":
    test_config()
