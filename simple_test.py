#!/usr/bin/env python3
import os
from pathlib import Path
from dotenv import load_dotenv

# 手动加载.env文件
env_file = Path(__file__).parent / ".env"
print(f"加载.env文件: {env_file}")
print(f"文件存在: {env_file.exists()}")

if env_file.exists():
    load_dotenv(env_file, override=True)
    print("环境变量:")
    print(f"  OPENAI_RERANK_API_BASE: {os.getenv('OPENAI_RERANK_API_BASE', '未设置')}")
    print(f"  OPENAI_RERANK_API_KEY: {os.getenv('OPENAI_RERANK_API_KEY', '未设置')}")
    print(f"  OPENAI_RERANK_MODEL: {os.getenv('OPENAI_RERANK_MODEL', '未设置')}")
    print(f"  RERANK_ENABLED: {os.getenv('RERANK_ENABLED', '未设置')}")
