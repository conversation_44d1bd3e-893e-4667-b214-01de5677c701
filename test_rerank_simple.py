#!/usr/bin/env python3
"""
简单的rerank功能测试
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_rerank():
    """测试rerank功能"""
    try:
        from core.rag.rerank import openai_rerank
        
        query = "什么是人工智能？"
        documents = [
            "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "今天天气很好，适合出去散步。",
            "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。"
        ]
        
        print("测试Rerank功能")
        print(f"查询: {query}")
        print(f"文档数量: {len(documents)}")
        
        results = await openai_rerank(
            query=query,
            documents=documents,
            top_n=3,
            api_key="sk-7a2f8e1d4c9b3a6x9y5z2w8q4r6t7u8i9o0p",
            model="qwen_rerank",
            base_url="http://8.137.120.199:6000/v1/rerank/score"
        )
        
        print("\nRerank结果:")
        for i, result in enumerate(results):
            index = result["index"]
            score = result["relevance_score"]
            doc = documents[index]
            print(f"  排名 {i+1}: 索引 {index}, 分数 {score:.4f}")
            print(f"    文档: {doc[:50]}...")
        
        print("\n✅ Rerank测试成功!")
        return True
        
    except Exception as e:
        print(f"\n❌ Rerank测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_rerank())
