#!/usr/bin/env python3
"""
直接测试rerank API调用
"""
import asyncio
import aiohttp


async def test_rerank_direct():
    """直接测试rerank API"""
    url = "http://8.137.120.199:6000/v1/rerank/score"
    headers = {
        "Authorization": "Bearer sk-7a2f8e1d4c9b3a6x9y5z2w8q4r6t7u8i9o0p",
        "Content-Type": "application/json",
        "accept": "application/json"
    }

    query = "什么是人工智能？"
    documents = [
        "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习。",
        "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
        "自然语言处理是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
        "今天天气很好，适合出去散步。"
    ]
    
    print("=" * 50)
    print("直接测试Rerank API")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Query: {query}")
    print(f"Documents: {len(documents)}")

    # Test each document against the query
    results = []
    try:
        async with aiohttp.ClientSession() as session:
            for i, doc in enumerate(documents):
                payload = {
                    "model": "qwen_rerank",
                    "encoding_format": "float",
                    "text_1": query,
                    "text_2": doc
                }

                print(f"\n测试文档 {i+1}: {doc[:50]}...")
                async with session.post(url, headers=headers, json=payload) as response:
                    print(f"HTTP状态码: {response.status}")

                    if response.status == 200:
                        result = await response.json()
                        score = result.get("score", 0.0)
                        results.append({"index": i, "score": score, "document": doc})
                        print(f"✅ 分数: {score:.4f}")
                    else:
                        error_text = await response.text()
                        print(f"❌ API调用失败: {error_text}")
                        results.append({"index": i, "score": 0.0, "document": doc})

        # Sort by score
        results.sort(key=lambda x: x["score"], reverse=True)

        print(f"\n最终排序结果:")
        for i, result in enumerate(results):
            print(f"  排名 {i+1}: 分数 {result['score']:.4f}")
            print(f"    文档: {result['document']}")

        return True
                    
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_rerank_direct())
